"""
WhatsApp invitation services with TokeChat integration
"""

import logging
from typing import Dict, Any
from .base import (
    InvitationServiceBase,
    InvitationResult,
    ServiceUnavailableError,
)
from api.crm.services.tokechat import TokeChatAPIService
from api.crm.services.evolution_api import EvolutionAPIService, EvolutionAPIError
from api.crm.utils.template import TemplateParser
from django.conf import settings

logger = logging.getLogger(__name__)


class WhatsAppInvitationService(InvitationServiceBase):
    """
    WhatsApp invitation service using TokeChat or Evolution API
    Will use Evolution API if available (environment variables are set), otherwise TokeChat
    """

    whatsapp_instance = None

    def __init__(self):
        self.parser = TemplateParser()
        if getattr(settings, "EVOLUTION_API_INSTANCE_NAME", None) is not None:
            self.whatsapp_instance = EvolutionAPIService()
        else:
            self.whatsapp_instance = TokeChatAPIService()

    def get_service_name(self) -> str:
        if self.whatsapp_instance:
            if isinstance(self.whatsapp_instance, EvolutionAPIService):
                return "WhatsApp (Evolution API)"
            elif isinstance(self.whatsapp_instance, TokeChatAPIService):
                return "WhatsApp (TokeChat)"
        return "There is no WhatsApp service available"

    def is_available(self) -> bool:
        try:
            if self.whatsapp_instance:
                if isinstance(self.whatsapp_instance, EvolutionAPIService):
                    response = self.whatsapp_instance.client.info.get_info()
                    return response.get("status") == 200

                elif isinstance(self.whatsapp_instance, TokeChatAPIService):
                    return self.whatsapp_instance.is_available()

            return False
        except Exception as e:
            logger.error(f"Error checking WhatsApp availability: {e}")
            return False

    def validate_template(self, template) -> bool:
        """Validate that template has ext_reference (flow ID) for TokeChat"""
        if not super().validate_template(template):
            return False

        # Si es una instancia de tokechat, validar que tenga ext_reference
        if isinstance(self.whatsapp_instance, TokeChatAPIService):
            return bool(template.ext_reference)

        # Caso contrario, validar con template parser
        result = self.parser.validate_template(template.body_text, template.type)
        is_valid = result.get("valid", False)
        if not is_valid:
            return False

        return True

    def send_invitation(
        self, enrollment, template, variables: Dict[str, Any] = None
    ) -> InvitationResult:
        """
        Send WhatsApp invitation using a WhatsApp Instance
        """
        try:
            # Validate template
            if not self.validate_template(template):
                return InvitationResult(
                    success=False,
                    message="Invalid template template",
                    error_details={"error_type": "invalid_template"},
                )

            # Get phone number
            phone_number = enrollment.phone_number or (
                enrollment.user.phone_number if enrollment.user else None
            )
            if not phone_number:
                return InvitationResult(
                    success=False,
                    message="No phone number available for WhatsApp invitation",
                    error_details={"error_type": "missing_phone"},
                )

            response = {}
            if isinstance(self.whatsapp_instance, EvolutionAPIService):
                response = self.whatsapp_instance.send_event_invitation_message(
                    phone_number=phone_number,
                    enrollment_id=enrollment.id,
                )

            elif isinstance(self.whatsapp_instance, TokeChatAPIService):
                # Send via TokeChat
                flow_variables = self.prepare_variables(enrollment, variables)

                response = self.whatsapp_instance.send_flow_message(
                    phone_number=phone_number,
                    flow_id=template.ext_reference,
                    variables=flow_variables,
                )
            else:
                raise Exception("Invalid WhatsApp instance")

            return InvitationResult(
                success=True,
                message="WhatsApp invitation sent successfully",
                external_id=response.get("message_id", ""),
            )

        except EvolutionAPIError as e:
            return InvitationResult(
                success=False,
                message=f"Failed to send WhatsApp invitation through Evolution API: {str(e)}",
                error_details={"error_type": "api_error", "details": str(e)},
            )
        except ServiceUnavailableError:
            return InvitationResult(
                success=False,
                message="TokeChat service is currently unavailable",
                error_details={"error_type": "service_unavailable"},
                retry_after=300,  # Retry after 5 minutes
            )

        except Exception as e:
            logger.error(f"WhatsApp invitation failed: {e}")
            return InvitationResult(
                success=False,
                message=f"Failed to send WhatsApp invitation: {str(e)}",
                error_details={"error_type": "api_error", "details": str(e)},
            )
