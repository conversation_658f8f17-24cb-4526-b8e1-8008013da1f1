# Evolution API Integration

Esta carpeta contiene la integración con Evolution API para WhatsApp, migrada desde TypeScript a Python siguiendo las mejores prácticas del proyecto con un enfoque orientado a objetos.

## Estructura

```
evolution_api/
├── __init__.py                  # Exports principales
├── client.py                   # Cliente principal y funciones de conveniencia
├── evolution_request.py        # Función base para requests HTTP
├── execute/                    # Módulos de operaciones
│   ├── __init__.py
│   ├── operations.py          # Clases POO para operaciones
│   ├── messages/             # Envío de mensajes
│   │   ├── send_text.py
│   │   ├── send_image.py
│   │   └── ...
│   ├── instance/             # Gestión de instancias
│   │   ├── create_instance_basic.py
│   │   ├── fetch_instances.py
│   │   └── ...
│   ├── chat/                 # Operaciones de chat
│   │   ├── find_contacts.py
│   │   └── ...
│   ├── groups/               # Gestión de grupos
│   │   ├── create_group.py
│   │   └── ...
│   ├── events/               # Configuración de eventos
│   │   ├── set_webhook.py
│   │   └── ...
│   ├── integrations/         # Integraciones
│   │   ├── set_chatwoot.py
│   │   └── ...
│   └── profile/              # Gestión de perfil
│       ├── fetch_profile.py
│       └── ...
├── examples.py               # Ejemplos de uso
└── README.md                 # Este archivo
```

## Configuración

Asegúrate de tener las siguientes variables de entorno configuradas:

```bash
EVOLUTION_API_KEY=tu_api_key_aqui
EVOLUTION_API_HOST=https://tu-servidor-evolution.com
```

## Uso Básico

### Interfaz Orientada a Objetos (Recomendado)

```python
from api.crm.services.evolution_api import EvolutionAPIClient

# Crear cliente
client = EvolutionAPIClient("mi_instancia")

# Enviar mensaje de texto
response = client.messages.send_text(
    remote_jid="***********",
    message_text="¡Hola desde Evolution API!"
)

# Enviar imagen
response = client.messages.send_image(
    remote_jid="***********",
    media="https://example.com/image.jpg",
    caption="Imagen enviada desde Python"
)

# Crear grupo
response = client.groups.create(
    subject="Mi Grupo",
    description="Grupo creado desde Python",
    participants=["***********", "51987654322"]
)

# Verificar números de WhatsApp
response = client.chat.check_numbers(["***********", "51987654322"])

# Configurar webhook
response = client.events.set_webhook(
    enabled=True,
    webhook_url="https://mi-webhook.com/evolution"
)
```

### Función de Conveniencia

```python
from api.crm.services.evolution_api import create_client

# Crear cliente usando función helper
client = create_client("mi_instancia")
response = client.messages.send_text("***********", "Hola!")
```

### Importar funciones directamente (Método alternativo)

```python
from api.crm.services.evolution_api.execute.messages.send_text import send_text
from api.crm.services.evolution_api.execute.instance.create_instance_basic import create_instance_basic

# Enviar mensaje de texto
response = send_text(
    instance_name="mi_instancia",
    remote_jid="***********",
    message_text="Hola, este es un mensaje de prueba"
)

# Crear instancia básica
response = create_instance_basic(
    instance_name="nueva_instancia",
    token="token_opcional"
)
```

## Funciones Migradas

### Messages API
- `send_text`: Enviar mensajes de texto
- `send_image`: Enviar imágenes
- `send_video`: Enviar videos
- `send_audio`: Enviar audios
- `send_document`: Enviar documentos
- `send_contact`: Enviar contactos
- `send_buttons`: Enviar mensajes con botones
- `send_list`: Enviar mensajes con listas
- `send_poll`: Enviar encuestas
- `send_reaction`: Enviar reacciones a mensajes
- `send_pix`: Enviar mensajes PIX
- `send_stories`: Enviar historias

### Instance API
- `create_instance_basic`: Crear instancia básica de WhatsApp
- `fetch_instances`: Obtener instancias existentes
- `delete_instance`: Eliminar instancia
- `instance_connect`: Conectar instancia
- `logout_instance`: Cerrar sesión de instancia
- `restart_instance`: Reiniciar instancia
- `set_presence`: Configurar estado de presencia
- `instance_settings`: Configurar ajustes de instancia
- `set_proxy`: Configurar proxy
- `find_proxy`: Obtener configuración de proxy

### Chat API
- `find_contacts`: Buscar contactos
- `check_number`: Verificar números de WhatsApp
- `find_messages`: Buscar mensajes
- `read_messages`: Marcar mensajes como leídos
- `read_single_message`: Marcar un mensaje como leído
- `delete_message`: Eliminar mensaje
- `block_contact`: Bloquear/desbloquear contacto
- `send_presence`: Enviar estado de presencia

### Groups API
- `create_group`: Crear grupos de WhatsApp
- `fetch_groups`: Buscar grupos (múltiples métodos)
- `fetch_groups_by_invite_code`: Buscar grupo por código de invitación
- `fetch_group_by_jid`: Buscar grupo por JID
- `fetch_all_groups`: Obtener todos los grupos
- `update_participants`: Actualizar participantes del grupo
- `join_group`: Unirse a grupo
- `leave_group`: Salir de grupo
- `update_group_name`: Actualizar nombre del grupo
- `fetch_invite_code`: Obtener código de invitación
- `update_group_description`: Actualizar descripción del grupo

### Events API
- `set_webhook`: Configurar webhook
- `find_webhook`: Obtener configuración de webhook

### Profile API
- `fetch_profile`: Obtener información de perfil
- `update_profile_name`: Actualizar nombre de perfil
- `update_profile_status`: Actualizar estado de perfil
- `fetch_business_profile`: Obtener perfil de negocio
- `fetch_privacy_settings`: Obtener configuraciones de privacidad

### Integrations API
- `set_chatwoot`: Configurar integración con Chatwoot
- `find_chatwoot`: Obtener configuración de Chatwoot

## Manejo de Errores

Todas las operaciones pueden lanzar `EvolutionAPIError` en caso de error:

```python
from api.crm.services.evolution_api import EvolutionAPIClient, EvolutionAPIError

client = EvolutionAPIClient("mi_instancia")

try:
    response = client.messages.send_text(
        remote_jid="***********",
        message_text="Mensaje de prueba"
    )
    print("Mensaje enviado:", response)
except EvolutionAPIError as e:
    print(f"Error de API: {e.message}")
    if e.status_code:
        print(f"Código de estado: {e.status_code}")
    if e.response_data:
        print(f"Datos de respuesta: {e.response_data}")
```

## Características del Cliente POO

### Lazy Loading
Los recursos se cargan solo cuando se acceden por primera vez:

```python
client = EvolutionAPIClient("mi_instancia")
# Los recursos no se crean hasta que se usan
messages_api = client.messages  # Aquí se crea MessagesAPI
```

### Reutilización
Una vez creado, el cliente reutiliza las instancias de recursos:

```python
client = EvolutionAPIClient("mi_instancia")
client.messages.send_text("***********", "Mensaje 1")
client.messages.send_text("51987654322", "Mensaje 2")  # Reutiliza la misma instancia
```

### Type Hints
Todas las clases incluyen type hints para mejor soporte del IDE:

```python
from api.crm.services.evolution_api import EvolutionAPIClient
from typing import Dict, Any

client: EvolutionAPIClient = EvolutionAPIClient("mi_instancia")
response: Dict[str, Any] = client.messages.send_text("***********", "Hola!")
```

## Migración de Funciones Adicionales

Para migrar funciones adicionales desde TypeScript:

1. Crear el archivo Python en la carpeta correspondiente
2. Seguir el patrón de las funciones existentes
3. Convertir camelCase a snake_case
4. Adaptar tipos TypeScript a Python type hints
5. Usar `evolution_request()` para hacer las llamadas HTTP
6. Agregar la función al mapeo en `operations.py`

## Logging

Todas las funciones incluyen logging para facilitar el debugging:

```python
import logging

# Configurar logging para ver los mensajes de Evolution API
logging.getLogger('api.crm.services.evolution_api').setLevel(logging.INFO)
```
