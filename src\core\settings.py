"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 5.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

import sys
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

STAGE = os.environ.get("STAGE", "DEVELOPMENT")

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get(
    "SECRET_KEY",
    "django-insecure-ale64@-9$x3d0^+@5llah#zp882lpc&**=@qoohz1afq-7yibb",
)

# SECURITY WARNING: don't run with debug turned on in production!

DEBUG = os.environ.get("DEBUG", "1") == "1"

ALLOWED_HOSTS = os.environ.get("ALLOWED_HOSTS", "localhost").split(",")

MP_ACCESS_TOKEN = os.environ.get(
    "MP_ACCESS_TOKEN",
    "",
)
MP_PUBLIC_KEY = os.environ.get(
    "MP_PUBLIC_KEY",
    "",
)


# Check if the runserver command is being used
if "runserver" in sys.argv:
    try:
        # Find the index of the 'runserver' command
        runserver_index = sys.argv.index("runserver")

        # Try to retrieve the host argument
        host_arg = sys.argv[
            runserver_index + 1
        ]  # Get the next argument after 'runserver'

        # Extract the host part (assuming the format is HOST:PORT)
        ALLOWED_HOSTS.append(host_arg.split(":")[0])
    except (IndexError, ValueError):
        # If no host argument is provided or there's an error in parsing,
        pass


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "drf_yasg",
    "rest_framework.authtoken",
    "django_filters",
    "corsheaders",
    "core.apps.CoreConfig",
    "api.crm.apps.CrmConfig",
    "api",
    "django_celery_results",
    "django_json_widget",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "core.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            os.path.join(BASE_DIR, "templates"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("POSTGRES_DB", "portals-db"),
        "USER": os.environ.get("POSTGRES_USER", "portals"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD", "portals"),
        "HOST": os.environ.get("POSTGRES_HOST", "db"),
        "PORT": os.environ.get("POSTGRES_PORT", "5432"),
    }
}

# CORS settings

CORS_ALLOWED_ORIGINS = os.environ.get(
    "CORS_ALLOWED_ORIGINS",
    "http://localhost:3000,http://localhost:4000",
).split(",")
CORS_ALLOW_HEADERS = [
    "content-type",
    "content-disposition",
    "Authorization",
    "referer",
]


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

AUTH_USER_MODEL = "core.User"

REST_FRAMEWORK = {
    "DEFAULT_FILTER_BACKENDS": ["django_filters.rest_framework.DjangoFilterBackend"],
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.PageNumberPagination",
    "PAGE_SIZE": 10,
    "MAX_PAGE_SIZE": 100,
    "PAGE_SIZE_QUERY_PARAM": "page_size",
}

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "America/Lima"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.environ.get(
    "DJANGO_STATIC_ROOT", os.path.join(BASE_DIR, "staticfiles")
)
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

# Secret files
SECRET_FILES_DIR = os.environ.get("SECRET_FILES_DIR", os.path.join(BASE_DIR, "secrets"))

# Google Workspace integration
GOOGLE_CALENDAR_ID = os.environ.get(
    "GOOGLE_CALENDAR_ID",
    "<EMAIL>",
)

STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"

STATIC_ROOT = os.environ.get("DJANGO_STATIC_ROOT", STATIC_ROOT)

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Swagger settings
if STAGE == "DEVELOPMENT":
    DEFAULT_API_URL = f"http://{ALLOWED_HOSTS[0]}:8001"
else:
    DEFAULT_API_URL = f"https://{ALLOWED_HOSTS[0]}"

SWAGGER_SETTINGS = {
    "DEFAULT_INFO": "docs.settings.api_info",
    # "DEFAULT_AUTO_SCHEMA_CLASS": "drf_yasg.inspectors.SwaggerAutoSchema",
    "DEFAULT_GENERATOR_CLASS": "docs.settings.APISchemeGenerator",
    "DEFAULT_API_URL": DEFAULT_API_URL,
    "DEFAULT_PAGINATOR_INSPECTOR": "api.inspectors.PageNumberPaginatorInspectorClass",
}


# Minio settings
MINIO_ENDPOINT = os.environ.get("MINIO_ENDPOINT", "minio:9000")
MINIO_PUBLIC_ENDPOINT = os.environ.get("MINIO_PUBLIC_ENDPOINT", "localhost:9000")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY", "minioadmin")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY", "minioadmin")
MINIO_SECURE = os.environ.get("MINIO_SECURE", "0") == "1"
MINIO_REGION = os.environ.get("MINIO_REGION", "pe-east-1")

if STAGE == "DEVELOPMENT":
    MINIO_BASE_URL = f"http://{MINIO_PUBLIC_ENDPOINT}"
    MINIO_INTERNAL_BASE_URL = f"http://{MINIO_ENDPOINT}"
else:
    MINIO_BASE_URL = f"https://{MINIO_PUBLIC_ENDPOINT}"
    MINIO_INTERNAL_BASE_URL = f"https://{MINIO_ENDPOINT}"
MINIO_PUBLIC_BUCKET = "public"
MINIO_ERP_BUCKET = "erp"

# CSRF settings only for STAGING, PRODUCTION
if STAGE != "DEVELOPMENT":
    CSRF_TRUSTED_ORIGINS = os.environ.get(
        "CSRF_TRUSTED_ORIGINS",
        "localhost",
    ).split(",")
    SECURE_PROXY_SSL_HEADER = (
        "HTTP_X_FORWARDED_PROTO",
        "https",
    )
    CSRF_COOKIE_DOMAIN = os.environ.get(
        "CSRF_COOKIE_DOMAIN",
        ".grupoceuperu.com",
    )
    SESSION_COOKIE_DOMAIN = os.environ.get(
        "SESSION_COOKIE_DOMAIN",
        ".grupoceuperu.com",
    )
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
    SECURE_SSL_REDIRECT = True


# Celery Configuration
CELERY_BROKER_URL = os.environ.get("CELERY_BROKER_URL", "redis://redis:6379/0")
CELERY_RESULT_BACKEND = os.environ.get("CELERY_RESULT_BACKEND", "redis://redis:6379/0")
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = os.environ.get("EMAIL_USE_TLS", "1") == "1"
EMAIL_HOST_USER = os.environ.get("EMAIL_HOST_USER", "")
EMAIL_HOST_PASSWORD = os.environ.get("EMAIL_HOST_PASSWORD", "")
DEFAULT_FROM_EMAIL = "CEU Centro de Especialización <<EMAIL>>"

APP_HOST = os.environ.get("APP_HOST", "http://localhost:4000")

PAYPAL_BASE_URL = os.environ.get("PAYPAL_BASE_URL", "")
PAYPAL_CLIENT_ID = os.environ.get("PAYPAL_CLIENT_ID", "")
PAYPAL_CLIENT_SECRET = os.environ.get("PAYPAL_CLIENT_SECRET", "")

# META WHATSAPP API
META_ACCESS_TOKEN = os.environ.get("META_ACCESS_TOKEN", "")
META_APP_ID = os.environ.get("META_APP_ID", "")
WHATSAPP_BUSINESS_ACCOUNT_ID = os.environ.get("WHATSAPP_BUSINESS_ACCOUNT_ID", "")
WHATSAPP_BUSINESS_PHONE_ID = os.environ.get("WHATSAPP_BUSINESS_PHONE_ID", "")

# TOKECHAT API
TOKECHAT_API_KEY = os.getenv("TOKECHAT_API_KEY", "")

# EVOLUTION API
EVOLUTION_API_ACCESS_TOKEN = os.environ.get("EVOLUTION_API_ACCESS_TOKEN", "")
EVOLUTION_API_HOST = os.environ.get("EVOLUTION_API_HOST", "")
EVOLUTION_API_INSTANCE_NAME = os.environ.get("EVOLUTION_API_INSTANCE_NAME", "")

APPSHEET_APP_ID = os.environ.get("APPSHEET_APP_ID", "")
APPSHEET_API_KEY = os.environ.get("APPSHEET_API_KEY", "")

# Redis Cache Configuration
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")
REDIS_DB = os.environ.get("REDIS_DB", "0")
REDIS_MAX_CONNECTIONS = int(os.environ.get("REDIS_MAX_CONNECTIONS", 50))
REDIS_CACHE_TIMEOUT = int(os.environ.get("REDIS_CACHE_TIMEOUT", 300))

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": REDIS_MAX_CONNECTIONS,
                "retry_on_timeout": True,
            },
        },
        "KEY_PREFIX": "portals_cache",
        "TIMEOUT": REDIS_CACHE_TIMEOUT,
    }
}

EXCHANGE_RATE_API_KEY = os.getenv("EXCHANGE_RATE_API_KEY")
MONTHLY_SALES_TARGET = float(os.getenv("MONTHLY_SALES_TARGET", 100000))
