"""
Utils for manage CRM WhatsApp templates
"""

import re
import json
from datetime import datetime, date
from django.utils import timezone
from django.utils.dateformat import format as date_format
from core.models.template import TemplateVariable


class TemplateParser:
    """
    Utility class for parsing templates with dynamic variables
    """

    def __init__(self):
        pass

    def parse_template(self, template_text, context_object, template_type=None):
        """
        Parse a template with both positional and named variables

        Args:
            template_text (str): Template text with variables like {{ variable name }}
            context_object: The base object for variable extraction
            template_type: ContentType of the template to get available variables

        Returns:
            str: Parsed template with variables replaced
        """
        if not template_text:
            return ""

        # First handle named variables if template_type is provided
        if template_type and context_object:
            template_text = self._replace_named_variables(
                template_text, context_object, template_type
            )

        return template_text

    def preview_template(self, template_text, template_type):
        """
        Generate a preview of the template using example values

        Args:
            template_text (str): Template text
            template_type: ContentType of the template

        Returns:
            str: Preview text with example values
        """
        if not template_text or not template_type:
            return template_text

        # Get all template variables for this template type
        template_variables = TemplateVariable.objects.filter(
            template_type=template_type
        )

        # Create context with example values
        variable_context = {}
        for template_var in template_variables:
            # Use the example value directly since it's already formatted for display
            variable_context[template_var.name] = (
                template_var.example or f"[{template_var.name}]"
            )

        return self._replace_named_variables(
            template_text, variable_context, template_type, preview=True
        )

    def _replace_variables(self, template_text, variable_context):
        """
        Replace variables with flexible whitespace handling
        """

        def replace_variable(match):
            var_name = match.group(1).strip()
            return variable_context.get(var_name, match.group(0))

        # Pattern that matches {{ variable_name }} with flexible whitespace
        pattern = r"\{\{\s*([^}]+?)\s*\}\}"
        return re.sub(pattern, replace_variable, template_text)

    def _replace_named_variables(
        self, template_text, context_object, template_type, preview=False
    ):
        """
        Replace named variables like {{ nombre }} with actual values
        """
        # Get all template variables for this template type
        template_variables = TemplateVariable.objects.filter(
            template_type=template_type
        )

        # Create a mapping of variable name to actual value
        variable_context = {}

        for template_var in template_variables:
            try:
                # Use example value if previewing
                raw_value = (
                    self._extract_value_from_path(context_object, template_var.path)
                    if not preview
                    else template_var.example
                )

                formatted_value = self._format_value(raw_value, template_var)
                variable_context[template_var.name] = formatted_value
            except Exception:
                # If extraction fails, use example value or empty string
                variable_context[template_var.name] = template_var.example or ""

        # Replace variables with flexible whitespace handling
        return self._replace_variables(template_text, variable_context)

    def _format_value(self, value, template_var):
        """
        Format a value based on its data type and format
        """
        if value is None:
            return template_var.example or ""

        data_type = template_var.data_type
        data_format = template_var.data_format

        try:
            # Handle different data types
            if data_type == TemplateVariable.ARRAY:
                return self._format_array(value)
            elif data_type == TemplateVariable.DATETIME:
                return self._format_datetime(value, data_format)
            elif data_type == TemplateVariable.DATE:
                return self._format_date(value, data_format)
            elif data_type == TemplateVariable.DECIMAL:
                return self._format_decimal(value, data_format)
            elif data_type == TemplateVariable.BOOLEAN:
                return "Sí" if value else "No"
            elif data_type == TemplateVariable.INTEGER:
                return str(int(value)) if value is not None else ""
            else:  # STRING or default
                return str(value) if value is not None else ""
        except Exception:
            # If formatting fails, return the raw value or example
            return str(value) if value is not None else template_var.example or ""

    def _format_array(self, value):
        """
        Format array values as bullet points
        """
        if isinstance(value, str):
            try:
                # Try to parse as JSON if it's a string
                value = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON, treat as single item
                return f"- {value}"

        if isinstance(value, (list, tuple)):
            if not value:
                return ""
            return "\n".join([f"- {item}" for item in value])
        else:
            return f"- {value}"

    def _format_datetime(self, value, data_format):
        """
        Format datetime values
        """
        if isinstance(value, str):
            try:
                # Try to parse string as datetime
                value = datetime.fromisoformat(value.replace("Z", "+00:00"))
            except (ValueError, AttributeError):
                return str(value)

        if not isinstance(value, (datetime, date)):
            return str(value)

        # Convert to local timezone if it's a datetime
        if isinstance(value, datetime) and timezone.is_aware(value):
            value = timezone.localtime(value)

        if data_format == TemplateVariable.DATETIME_FULL:
            return date_format(value, "l, j \\d\\e F \\d\\e Y \\a \\l\\a\\s H:i")
        elif data_format == TemplateVariable.DATETIME_SHORT:
            return date_format(value, "d/m/Y H:i")
        elif data_format == TemplateVariable.TIME_12H:
            return date_format(value, "g:i A")
        elif data_format == TemplateVariable.TIME_24H:
            return date_format(value, "H:i")
        elif data_format == TemplateVariable.DATE_LONG:
            return date_format(value, "l, j \\d\\e F \\d\\e Y")
        elif data_format == TemplateVariable.DATE_SHORT:
            return date_format(value, "d/m/Y")
        elif data_format == TemplateVariable.DATE_WEEKDAY_MEDIUM:
            return date_format(value, "l, j \\d\\e F")
        else:
            return date_format(value, "d/m/Y H:i")

    def _format_date(self, value, data_format):
        """
        Format date values
        """
        if isinstance(value, str):
            try:
                # Try to parse string as date
                value = datetime.fromisoformat(value.replace("Z", "+00:00")).date()
            except (ValueError, AttributeError):
                return str(value)

        if isinstance(value, datetime):
            value = value.date()

        if not isinstance(value, date):
            return str(value)

        if data_format == TemplateVariable.DATE_LONG:
            return date_format(value, "l, j \\d\\e F \\d\\e Y")
        elif data_format == TemplateVariable.DATE_SHORT:
            return date_format(value, "d/m/Y")
        elif data_format == TemplateVariable.DATE_WEEKDAY_MEDIUM:
            return date_format(value, "l, j \\d\\e F")
        else:
            return date_format(value, "d/m/Y")

    def _format_decimal(self, value, data_format):
        """
        Format decimal/currency values
        """
        try:
            if isinstance(value, str):
                value = float(value)

            if data_format == TemplateVariable.CURRENCY:
                return f"${value:,.2f}"
            else:
                return f"{value:,.2f}"
        except (ValueError, TypeError):
            return str(value)

    def _extract_value_from_path(self, obj, path):
        """
        Extract value from object using Django-style path (e.g., 'user__first_name')

        Args:
            obj: Base object
            path (str): Path to the value (e.g., 'user__first_name')

        Returns:
            The extracted value or None
        """
        if not obj or not path:
            return None

        current_obj = obj
        path_parts = path.split("__")

        for part in path_parts:
            if current_obj is None:
                return None

            try:
                if hasattr(current_obj, part):
                    current_obj = getattr(current_obj, part)
                    # If it's a callable (like a method), call it
                    if callable(current_obj):
                        current_obj = current_obj()
                else:
                    return None
            except Exception:
                return None

        return current_obj

    def validate_template(self, template_text, template_type):
        """
        Validate that all variables in template exist for the given template type

        Args:
            template_text (str): Template text
            template_type: ContentType of the template

        Returns:
            dict: {'valid': bool, 'errors': list, 'unknown_variables': list}
        """
        if not template_text:
            return {"valid": True, "errors": [], "unknown_variables": []}

        # Extract all variables from template text
        variable_pattern = r"\{\{\s*(\w+)\s*\}\}"
        found_variables = re.findall(variable_pattern, template_text)

        if not template_type:
            return {
                "valid": len(found_variables) == 0,
                "errors": (
                    ["Template type is required for validation"]
                    if found_variables
                    else []
                ),
                "unknown_variables": found_variables,
            }

        # Get available variables for this template type
        available_variables = set(
            TemplateVariable.objects.filter(template_type=template_type).values_list(
                "name", flat=True
            )
        )

        # Check for unknown variables
        unknown_variables = [
            var for var in found_variables if var not in available_variables
        ]

        errors = []
        if unknown_variables:
            errors.append(f"Unknown variables: {', '.join(unknown_variables)}")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "unknown_variables": unknown_variables,
        }


# Convenience function for quick template parsing
def parse_template(template_text, context_object, template_type=None):
    """
    Quick function to parse a template
    """
    parser = TemplateParser()
    return parser.parse_template(template_text, context_object, template_type)
