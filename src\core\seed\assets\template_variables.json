{"template_types": [{"content_type_model": "EventScheduleEnrollment", "name": "Recordatorio de Eventos"}], "template_variables": [{"template_type_content_type": "EventScheduleEnrollment", "name": "nombre", "path": "first_name", "example": "<PERSON><PERSON>", "description": "Nombre del usuario inscrito en el evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "a<PERSON>lid<PERSON>", "path": "last_name", "example": "<PERSON>ti <PERSON>", "description": "Apellido del usuario inscrito en el evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "correo", "path": "email", "example": "<EMAIL>", "description": "Coorreo electrónico gmail del usuario inscrito en el evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "universidad", "path": "university", "example": "Universidad Nacional de Ingeniería", "description": "Universidad a la que pertenece el usuario inscrito en el evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "intereses de oferta", "path": "interests", "example": "[\"PE Finanzas Avanzadas\", \"PE Ciencia de Datos\"]", "description": "Lista de intereses de oferta, será formateado como una lista no numerada", "data_type": "ARRAY"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "instructor", "path": "event_schedule__instructor__full_name", "example": "<PERSON><PERSON>", "description": "Nombre completo del instructor del evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "titulo de instructor", "path": "event_schedule__instructor__title", "example": "Msc(c) Econometría Bancaria y Financiera", "description": "T<PERSON><PERSON>lo del instructor del evento", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "meet link", "path": "event_schedule__ext_event_link", "example": "https://meet.google.com/abc-def-ghi", "description": "Link para acceder al evento virtual", "data_type": "STRING"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "hora de inicio", "path": "event_schedule__start_date", "example": "10:00 am", "description": "Hora de inicio del evento (formato 12h)", "data_type": "DATETIME", "data_format": "TIME_12H"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "fecha de inicio", "path": "event_schedule__start_date", "example": "Viernes, 11 de julio", "description": "Fecha de inicio del evento", "data_type": "DATETIME", "data_format": "DATE_WEEKDAY_MEDIUM"}, {"template_type_content_type": "EventScheduleEnrollment", "name": "evento", "path": "event_schedule__name", "example": "Taller: Programa de Especialización en Finanzas Avanzadas", "description": "Nombre del evento", "data_type": "STRING"}]}